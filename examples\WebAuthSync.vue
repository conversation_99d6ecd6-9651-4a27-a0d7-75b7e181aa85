<!--
  Web 端认证同步示例组件
  这个组件展示了如何在 web 端使用 better-auth 并同步状态到扩展
-->
<template>
  <div class="auth-sync-demo">
    <h2>Web 端认证状态同步示例</h2>
    
    <div class="auth-status">
      <h3>当前认证状态:</h3>
      <pre>{{ JSON.stringify(session.data, null, 2) }}</pre>
    </div>

    <div class="actions">
      <button @click="login" :disabled="session.isPending">
        {{ session.isPending ? '登录中...' : '登录' }}
      </button>
      
      <button @click="logout" :disabled="session.isPending">
        {{ session.isPending ? '登出中...' : '登出' }}
      </button>
      
      <button @click="manualSync">
        手动同步到扩展
      </button>
    </div>

    <div class="sync-status">
      <h3>同步状态:</h3>
      <p>PostMessage 同步: {{ syncOptions.enablePostMessage ? '✅' : '❌' }}</p>
      <p>Chrome Runtime 同步: {{ syncOptions.enableChromeRuntime ? '✅' : '❌' }}</p>
      <p>扩展检测: {{ isExtensionAvailable ? '✅ 已安装' : '❌ 未检测到' }}</p>
    </div>

    <div class="instructions">
      <h3>使用说明:</h3>
      <ol>
        <li>确保浏览器扩展已安装并启用</li>
        <li>在此页面登录或登出</li>
        <li>打开扩展的 popup 或 dashboard 页面</li>
        <li>观察扩展端的认证状态是否同步更新</li>
      </ol>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { authClient } from '@/auth/auth-client'
import { authSync } from '@/utils/authSync'

// 获取 better-auth session
const session = authClient.useSession()

// 同步选项
const syncOptions = ref({
  enablePostMessage: true,
  enableChromeRuntime: true
})

// 扩展可用性检测
const isExtensionAvailable = ref(false)

// 登录函数
const login = async () => {
  try {
    await authClient.signIn.email({
      email: '<EMAIL>',
      password: 'password123'
    })
  } catch (error) {
    console.error('登录失败:', error)
  }
}

// 登出函数
const logout = async () => {
  try {
    await authClient.signOut()
  } catch (error) {
    console.error('登出失败:', error)
  }
}

// 手动同步
const manualSync = () => {
  authSync.sync()
  console.log('手动同步已触发')
}

// 检测扩展是否可用
const checkExtensionAvailability = () => {
  if (typeof chrome !== 'undefined' && chrome.runtime) {
    try {
      // 尝试发送消息到扩展
      chrome.runtime.sendMessage('extension-id', { type: 'ping' }, (response) => {
        isExtensionAvailable.value = !chrome.runtime.lastError
      })
    } catch (error) {
      isExtensionAvailable.value = false
    }
  }
}

onMounted(() => {
  // 检测扩展可用性
  checkExtensionAvailability()
  
  // 确保认证同步已初始化
  authSync.init()
})
</script>

<style scoped>
.auth-sync-demo {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.auth-status, .actions, .sync-status, .instructions {
  margin: 20px 0;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.actions button {
  margin: 5px;
  padding: 10px 15px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
}

.actions button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.actions button:hover:not(:disabled) {
  background: #0056b3;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 3px;
  overflow-x: auto;
}

ol {
  padding-left: 20px;
}

li {
  margin: 5px 0;
}
</style>
