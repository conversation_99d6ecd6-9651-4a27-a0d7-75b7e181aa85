import { computed, ref } from 'vue'
import { authClientWithSync } from '@/auth/auth-client'

export interface AuthUser {
  id: string
  name: string
  email: string
  avatarUrl?: string
}

export interface AuthComposable {
  currentUser: ComputedRef<AuthUser | null>
  session: any
  isAuthenticated: ComputedRef<boolean>
  refreshSession: () => Promise<void>
  logout: () => Promise<void>
}

// 模块级单例模式
let isInitialized = false
const _currentUser = ref<AuthUser | null>(null)

// 初始化逻辑（只会执行一次）
const initializeAuth = () => {
  if (isInitialized) return
  
  // 读取初始值
  if (typeof chrome !== 'undefined' && chrome.storage) {
    chrome.storage.local.get(['user'], (result) => {
      _currentUser.value = result.user || null
    })

    // 设置监听（只会注册一次）
    chrome.storage.onChanged.addListener((changes, areaName) => {
      if (areaName === 'local' && changes.user) {
        _currentUser.value = changes.user.newValue
      }
    })
  }

  isInitialized = true
}

export default function useAuth(): AuthComposable {
  initializeAuth()

  // 获取 better-auth session
  const session = authClientWithSync.useSession()
  
  const endpoint = import.meta.env.VITE_ENDPOINT

  const refreshSession = async () => {
    console.log('--------- refreshSession start ---------')
    const response = await fetch(`${endpoint}/api/_auth/session`, {
      headers: {
        accept: 'application/json',
      },
    })

    if (response.ok) {
      const data = await response.json()
      const user = data?.user || null
      
      // 同时更新两个状态
      _currentUser.value = user
      if (typeof chrome !== 'undefined' && chrome.storage) {
        chrome.storage.local.set({ 'user': user })
      }
    } else {
      _currentUser.value = null
      if (typeof chrome !== 'undefined' && chrome.storage) {
        chrome.storage.local.set({ 'user': null })
      }
    }
  }

  const logout = async () => {
    const response = await fetch(`${endpoint}/api/_auth/session`, {
      method: 'DELETE',
    })

    if (response.ok) {
      _currentUser.value = null
      if (typeof chrome !== 'undefined' && chrome.storage) {
        chrome.storage.local.set({ user: null })
      }
    }
  }

  return {
    currentUser: computed(() => _currentUser.value),
    session,
    isAuthenticated: computed(() => !!_currentUser.value),
    refreshSession,
    logout
  }
}
